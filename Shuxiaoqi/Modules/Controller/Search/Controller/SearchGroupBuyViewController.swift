//搜索-团购
import UIKit
import SnapKit
import Kingfisher
import CoreLocation

class SearchGroupBuyViewController: UIViewController, Searchable, CLLocationManagerDelegate {
    // MARK: - UI 组件
    private let filterContainer = UIView()
    private var filterButtons: [UIButton] = []
    private var selectedFilterIndex = 0

    private lazy var tableView: UITableView = {
        let tv = UITableView(frame: .zero, style: .plain)
        tv.backgroundColor = UIColor(hex: "#F5F5F5")
        tv.separatorStyle = .none
        tv.dataSource = self
        tv.delegate = self
        tv.register(GroupBuyTableViewCell.self, forCellReuseIdentifier: GroupBuyTableViewCell.reuseIdentifier)
        return tv
    }()

    // MARK: - 数据
    private var groupBuys: [GroupBuyShopItem] = []
    private var keywordCache: [String: [GroupBuyShopItem]] = [:]
    private var currentKeyword: String?
    private var pendingKeyword: String?

    /// 空数据占位
    private lazy var emptyStateView: UIView = {
        let v = UIView()
        let img = UIImageView(image: UIImage(named: "empty_data_placeholder_image"))
        img.contentMode = .scaleAspectFit
        img.translatesAutoresizingMaskIntoConstraints = false
        v.addSubview(img)
        let lbl = UILabel()
        lbl.text = "暂无相关团购店铺"
        lbl.textColor = .lightGray
        lbl.font = .systemFont(ofSize: 14)
        lbl.translatesAutoresizingMaskIntoConstraints = false
        v.addSubview(lbl)
        NSLayoutConstraint.activate([
            img.centerXAnchor.constraint(equalTo: v.centerXAnchor),
            img.centerYAnchor.constraint(equalTo: v.centerYAnchor, constant: -20),
            img.widthAnchor.constraint(equalToConstant: 120),
            img.heightAnchor.constraint(equalToConstant: 120),
            lbl.topAnchor.constraint(equalTo: img.bottomAnchor, constant: 12),
            lbl.centerXAnchor.constraint(equalTo: v.centerXAnchor)
        ])
        return v
    }()

    // 定位
    private let locationManager = CLLocationManager()
    private var currentLatitude: Double?
    private var currentLongitude: Double?

    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .white
        setupLocation()
        setupUI()
        // 初始显示空数据占位
        updateEmptyState()
    }

    private func setupUI() {
        view.addSubview(filterContainer)
        view.addSubview(tableView)
        setupFilterBar()
        filterContainer.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide)
            make.left.right.equalToSuperview()
            make.height.equalTo(44)
        }
        tableView.snp.makeConstraints { make in
            make.top.equalTo(filterContainer.snp.bottom)
            make.left.right.bottom.equalToSuperview()
        }
    }

    private func setupFilterBar() {
        let titles = ["综合排序", "最新上线"]
        var prev: UIButton?
        for (idx, title) in titles.enumerated() {
            let btn = UIButton(type: .custom)
            btn.setTitle(title, for: .normal)
            btn.setTitleColor(UIColor(hex: "#777777"), for: .normal)
            btn.titleLabel?.font = .systemFont(ofSize: 14)
            btn.tag = idx
            btn.addTarget(self, action: #selector(filterTapped(_:)), for: .touchUpInside)
            filterContainer.addSubview(btn)
            btn.snp.makeConstraints { make in
                make.centerY.equalToSuperview()
                if let p = prev { make.left.equalTo(p.snp.right).offset(24) } else { make.left.equalToSuperview().offset(16) }
                make.width.equalTo(84)
                make.height.equalTo(29)
            }
            filterButtons.append(btn)
            prev = btn
        }
        selectedFilterIndex = 0
    }

    @objc private func filterTapped(_ sender: UIButton) {
        guard sender.tag != selectedFilterIndex else { return }
        selectedFilterIndex = sender.tag
        updateFilterUI()
    }

    private func updateFilterUI() {
        for btn in filterButtons {
            let sel = btn.tag == selectedFilterIndex
            if sel {
                btn.setTitleColor(.white, for: .normal)
                btn.titleLabel?.font = .boldSystemFont(ofSize: 14)
                btn.backgroundColor = .clear
                btn.layer.cornerRadius = 14.5
                applyGradient(to: btn)
                filterContainer.bringSubviewToFront(btn)
            } else {
                btn.setTitleColor(UIColor(hex: "#777777"), for: .normal)
                btn.titleLabel?.font = .systemFont(ofSize: 14)
                removeGradient(from: btn)
                btn.backgroundColor = UIColor(hex: "#E7E7E7")
                btn.layer.cornerRadius = 14.5
            }
        }
    }

    private func applyGradient(to button: UIButton) {
        removeGradient(from: button)
        let g = CAGradientLayer()
        g.colors = [UIColor(hex: "#FF8D36").cgColor, UIColor(hex: "#FF5858").cgColor]
        g.startPoint = CGPoint(x: 0, y: 0.5)
        g.endPoint = CGPoint(x: 1, y: 0.5)
        g.cornerRadius = 14.5
        g.frame = button.bounds
        g.name = "btnGradient"
        button.layer.insertSublayer(g, at:0)
    }
    private func removeGradient(from button: UIButton) {
        button.layer.sublayers?.removeAll(where: {$0.name=="btnGradient"})
    }

    private var didApplyInit = false
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        if !didApplyInit {
            updateFilterUI()
            didApplyInit = true
        }
        for btn in filterButtons {
            btn.layer.sublayers?.forEach { if $0.name=="btnGradient" { $0.frame = btn.bounds } }
        }
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        // 确保在子控制器显示时渐变正常渲染
        updateFilterUI()
    }

    // MARK: - 网络请求
    private func fetchGroupBuyShops(keyword: String?) {
        let lat = currentLatitude ?? 0
        let lon = currentLongitude ?? 0
        let request = GroupBuyShopSearchRequest(latitude: lat, longitude: lon, page: 0, size: 10, smartBy: nil, categoryId: nil, priceNum: nil, distance: nil, searchValue: keyword)

        APIManager.shared.searchGroupBuyShop(request: request) { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    self?.groupBuys = response.data.list
                    // 缓存结果
                    let key = keyword ?? ""
                    self?.keywordCache[key] = self?.groupBuys
                    self?.tableView.reloadData()
                case .failure(let error):
                    print("GroupBuy search error: \(error)")
                }
                self?.updateEmptyState()
            }
        }
    }

    // MARK: - Searchable
    func search(with keyword: String) {
        print("[GroupBuy] 搜索关键词：\(keyword)")

        // 缓存检查
        let key = keyword
        if let cached = keywordCache[key], !cached.isEmpty {
            print("[GroupBuy] 使用缓存，跳过请求")
            groupBuys = cached
            tableView.reloadData()
            updateEmptyState()
            return
        }

        // 检查定位
        guard let lat = currentLatitude, let lon = currentLongitude, lat != 0, lon != 0 else {
            print("[GroupBuy] 定位未就绪，等待定位")
            pendingKeyword = keyword
            ensureLocationAuthorization()
            return
        }

        fetchGroupBuyShops(keyword: keyword)
    }

    private func updateEmptyState() {
        tableView.backgroundView = groupBuys.isEmpty ? emptyStateView : nil
    }

    // MARK: - 定位
    private func setupLocation() {
        locationManager.delegate = self
        locationManager.desiredAccuracy = kCLLocationAccuracyHundredMeters
        if CLLocationManager.authorizationStatus() == .notDetermined {
            locationManager.requestWhenInUseAuthorization()
        } else {
            locationManager.startUpdatingLocation()
        }
    }

    private func ensureLocationAuthorization() {
        switch CLLocationManager.authorizationStatus() {
        case .notDetermined:
            locationManager.requestWhenInUseAuthorization()
        case .authorizedAlways, .authorizedWhenInUse:
            locationManager.startUpdatingLocation()
        default:
            presentLocationDeniedAlert()
        }
    }

    /// 弹窗提醒用户开启定位
    private func presentLocationDeniedAlert() {
        let alert = UIAlertController(title: "需要位置信息",
                                      message: "为了获取附近团购店铺，请在\"设置-隐私-定位服务\"中允许树小柒访问您的位置。",
                                      preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        alert.addAction(UIAlertAction(title: "前往设置", style: .default) { _ in
            guard let url = URL(string: UIApplication.openSettingsURLString),
                  UIApplication.shared.canOpenURL(url) else { return }
            UIApplication.shared.open(url)
        })
        present(alert, animated: true)
    }

    // MARK: - CLLocationManagerDelegate
    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        guard let loc = locations.last else { return }
        currentLatitude = loc.coordinate.latitude
        currentLongitude = loc.coordinate.longitude
        manager.stopUpdatingLocation()

        if let keyword = pendingKeyword {
            pendingKeyword = nil
            fetchGroupBuyShops(keyword: keyword)
        }
    }

    func locationManagerDidChangeAuthorization(_ manager: CLLocationManager) {
        switch manager.authorizationStatus {
        case .authorizedAlways, .authorizedWhenInUse:
            manager.startUpdatingLocation()
        case .denied, .restricted:
            presentLocationDeniedAlert()
        default:
            break
        }
    }
}

extension SearchGroupBuyViewController: UITableViewDataSource, UITableViewDelegate {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int { groupBuys.count }
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let cell = tableView.dequeueReusableCell(withIdentifier: GroupBuyTableViewCell.reuseIdentifier, for: indexPath) as? GroupBuyTableViewCell else { return UITableViewCell() }
        cell.configure(with: groupBuys[indexPath.row])
        return cell
    }
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat { 180 }
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        let item = groupBuys[indexPath.row].goodResultVO
        let webVC = WebViewController(path: "localLifePage/storePage/storePage?shopId=\(String(describing: item?.goodId))", title: "团购详情")
        navigationController?.pushViewController(webVC, animated: true)
    }
}
