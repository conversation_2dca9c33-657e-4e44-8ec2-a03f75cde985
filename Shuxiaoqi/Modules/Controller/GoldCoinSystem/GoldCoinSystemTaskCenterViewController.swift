//
//  GoldCoinSystemTaskCenterViewController.swift
//  Shuxiaoqi
//
//  Created by yong<PERSON>ng ye on 2025/8/12.
//

import UIKit
import SnapKit

// MARK: - 金币系统任务中心控制器
class GoldCoinSystemTaskCenterViewController: BaseViewController {

    // MARK: - 数据模型
    private var currentGoldCoins: Int = 8888
    private var dailyEarnableCoins: Int = 2000
    private var exchangeRate: String = "10000金币=1元现金"

    // 签到数据
    private var checkInDays: [CheckInDay] = []
    private var currentCheckInDay: Int = 3 // 当前签到到第几天

    // 观看视频进度
    private var videoWatchProgress: Float = 0.3 // 30%
    private var videoWatchedSeconds: Int = 180 // 已观看3分钟
    private var videoTotalSeconds: Int = 600 // 总共10分钟

    // MARK: - UI 组件

    // 滚动视图
    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.backgroundColor = .white
        scrollView.showsVerticalScrollIndicator = false
        return scrollView
    }()

    // 滚动内容容器
    private lazy var scrollContentView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        return view
    }()

    // 头部背景容器 - #F7F7F7色，20圆角，168高度
    private lazy var headerBackgroundView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#F7F7F7")
        view.layer.cornerRadius = 20
        return view
    }()

    // 当前金币背景图 - 橙色背景
    private lazy var goldCoinBackgroundView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "gold_coin_task_center_header_bg") // 预命名的橙色背景图
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = 16
        return imageView
    }()

    // 当前金币标题
    private lazy var currentGoldTitleLabel: UILabel = {
        let label = UILabel()
        label.text = "当前金币"
        label.textColor = .white
        label.font = .systemFont(ofSize: 16, weight: .medium)
        return label
    }()

    // 金币数量
    private lazy var goldCoinAmountLabel: UILabel = {
        let label = UILabel()
        label.text = "\(currentGoldCoins)"
        label.textColor = .white
        label.font = .systemFont(ofSize: 32, weight: .bold)
        return label
    }()

    // 提现按钮
    private lazy var withdrawButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("提现", for: .normal)
        button.setTitleColor(.appThemeOrange, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 14, weight: .medium)
        button.backgroundColor = .white
        button.layer.cornerRadius = 16
        button.addTarget(self, action: #selector(withdrawButtonTapped), for: .touchUpInside)
        return button
    }()

    // 今日可赚标签
    private lazy var dailyEarnLabel: UILabel = {
        let label = UILabel()
        label.text = "今日可赚 \(dailyEarnableCoins)金币"
        label.textColor = .white
        label.font = .systemFont(ofSize: 14)
        return label
    }()

    // 兑换比例标签
    private lazy var exchangeRateLabel: UILabel = {
        let label = UILabel()
        label.text = exchangeRate
        label.textColor = UIColor(hex: "#666666")
        label.font = .systemFont(ofSize: 14)
        label.backgroundColor = .clear
        label.textAlignment = .left
        return label
    }()

    // MARK: - 任务卡片组件

    // 发布视频任务行
    private lazy var publishVideoTaskView: TaskRowView = {
        let view = TaskRowView()
        view.configure(
            icon: UIImage(named: "gold_coin_task_publish_video_icon"),
            title: "发布视频",
            subtitle: "首次发布视频即可获得奖励",
            reward: "+100金币",
            buttonTitle: "去完成",
            isCompleted: true
        )
        view.onButtonTapped = { [weak self] in
            self?.publishVideoTaskTapped()
        }
        return view
    }()

    // 发布视频分割线
    private lazy var publishVideoSeparator: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#E5E5E5")
        return view
    }()

    // 连续签到容器（包含任务行和签到日历）
    private lazy var checkInContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        return view
    }()

    // 连续签到任务行
    private lazy var checkInTaskView: TaskRowView = {
        let view = TaskRowView()
        view.configure(
            icon: UIImage(named: "gold_coin_task_checkin_icon"),
            title: "连续签到",
            subtitle: "连续签到7天可获得额外奖励",
            reward: "+100金币",
            buttonTitle: "签到",
            isCompleted: false
        )
        view.onButtonTapped = { [weak self] in
            self?.checkInTaskTapped()
        }
        return view
    }()

    // 签到日历容器
    private lazy var checkInCalendarView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()

    // 连续签到分割线
    private lazy var checkInSeparator: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#E5E5E5")
        return view
    }()

    // 邀请好友任务行
    private lazy var inviteFriendsTaskView: TaskRowView = {
        let view = TaskRowView()
        view.configure(
            icon: UIImage(named: "gold_coin_task_invite_friends_icon"),
            title: "邀请好友",
            subtitle: "邀请1位好友注册",
            reward: "+200金币",
            buttonTitle: "去邀请",
            isCompleted: false
        )
        view.onButtonTapped = { [weak self] in
            self?.inviteFriendsTaskTapped()
        }
        return view
    }()

    // 邀请好友分割线
    private lazy var inviteFriendsSeparator: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#E5E5E5")
        return view
    }()

    // 观看视频容器（包含任务行和进度条）
    private lazy var watchVideoContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        return view
    }()

    // 观看视频任务行
    private lazy var watchVideoTaskView: TaskRowView = {
        let view = TaskRowView()
        view.configure(
            icon: UIImage(named: "gold_coin_task_watch_video_icon"),
            title: "观看视频",
            subtitle: "观看视频10分钟",
            reward: "+100金币",
            buttonTitle: "去观看",
            isCompleted: false
        )
        view.onButtonTapped = { [weak self] in
            self?.watchVideoTaskTapped()
        }
        return view
    }()

    // 观看视频进度条
    private lazy var videoProgressView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()

    private lazy var videoProgressBackgroundView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#E5E5E5")
        view.layer.cornerRadius = 4
        return view
    }()

    private lazy var videoProgressForegroundView: UIView = {
        let view = UIView()
        view.backgroundColor = .appThemeOrange
        view.layer.cornerRadius = 4
        return view
    }()

    private lazy var videoProgressLabel: UILabel = {
        let label = UILabel()
        label.text = "已观看3分钟，还差7分钟"
        label.textColor = UIColor(hex: "#666666")
        label.font = .systemFont(ofSize: 12)
        return label
    }()

    // MARK: - 生命周期
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupData()
    }

    // MARK: - 设置方法

    private func setupUI() {
        navTitle = "任务中心"
        contentView.backgroundColor = .white

        // 添加滚动视图
        contentView.addSubview(scrollView)
        scrollView.addSubview(scrollContentView)

        // 添加头部背景容器
        scrollContentView.addSubview(headerBackgroundView)

        // 添加当前金币背景图到头部背景容器
        headerBackgroundView.addSubview(goldCoinBackgroundView)
        goldCoinBackgroundView.addSubview(currentGoldTitleLabel)
        goldCoinBackgroundView.addSubview(goldCoinAmountLabel)
        goldCoinBackgroundView.addSubview(withdrawButton)
        goldCoinBackgroundView.addSubview(dailyEarnLabel)

        // 添加兑换比例标签到头部背景容器
        headerBackgroundView.addSubview(exchangeRateLabel)

        // 添加任务行和分割线
        scrollContentView.addSubview(publishVideoTaskView)
        scrollContentView.addSubview(publishVideoSeparator)

        // 添加连续签到容器
        scrollContentView.addSubview(checkInContainerView)
        checkInContainerView.addSubview(checkInTaskView)
        checkInContainerView.addSubview(checkInCalendarView)
        scrollContentView.addSubview(checkInSeparator)

        // 添加邀请好友任务
        scrollContentView.addSubview(inviteFriendsTaskView)
        scrollContentView.addSubview(inviteFriendsSeparator)

        // 添加观看视频容器
        scrollContentView.addSubview(watchVideoContainerView)
        watchVideoContainerView.addSubview(watchVideoTaskView)
        watchVideoContainerView.addSubview(videoProgressView)

        // 设置进度条
        videoProgressView.addSubview(videoProgressBackgroundView)
        videoProgressView.addSubview(videoProgressForegroundView)
        videoProgressView.addSubview(videoProgressLabel)

        setupConstraints()
        setupCheckInCalendar()
    }

    private func setupData() {
        // 初始化签到数据
        checkInDays = [
            CheckInDay(day: "周一", reward: 10, isCheckedIn: true),
            CheckInDay(day: "周二", reward: 20, isCheckedIn: true),
            CheckInDay(day: "周三", reward: 30, isCheckedIn: true),
            CheckInDay(day: "周四", reward: 40, isCheckedIn: false),
            CheckInDay(day: "周五", reward: 50, isCheckedIn: false),
            CheckInDay(day: "周六", reward: 60, isCheckedIn: false),
            CheckInDay(day: "周日", reward: 100, isCheckedIn: false)
        ]

        updateVideoProgress()
    }

    private func setupConstraints() {
        // 滚动视图约束
        scrollView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        scrollContentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        // 头部背景容器约束 - 距离顶部16pt，左右各16pt，高度168pt
        headerBackgroundView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(168)
        }

        // 当前金币背景图约束 - 在头部背景容器上方
        goldCoinBackgroundView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(120) // 调整高度，为下方文案留空间
        }

        // 当前金币标题约束
        currentGoldTitleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.left.equalToSuperview().offset(20)
        }

        // 金币数量约束
        goldCoinAmountLabel.snp.makeConstraints { make in
            make.top.equalTo(currentGoldTitleLabel.snp.bottom).offset(8)
            make.left.equalTo(currentGoldTitleLabel)
        }

        // 提现按钮约束
        withdrawButton.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.right.equalToSuperview().offset(-20)
            make.width.equalTo(60)
            make.height.equalTo(32)
        }

        // 今日可赚标签约束
        dailyEarnLabel.snp.makeConstraints { make in
            make.bottom.equalToSuperview().offset(-20)
            make.left.equalTo(currentGoldTitleLabel)
        }

        // 兑换比例标签约束 - 在当前金币背景图下方+8pt，左边距离背景View +16pt
        exchangeRateLabel.snp.makeConstraints { make in
            make.top.equalTo(goldCoinBackgroundView.snp.bottom).offset(8)
            make.left.equalToSuperview().offset(16)
            make.bottom.equalToSuperview().offset(-16)
        }

        // 发布视频任务行约束
        publishVideoTaskView.snp.makeConstraints { make in
            make.top.equalTo(headerBackgroundView.snp.bottom).offset(16)
            make.left.right.equalToSuperview()
            make.height.equalTo(80)
        }

        // 发布视频分割线约束
        publishVideoSeparator.snp.makeConstraints { make in
            make.top.equalTo(publishVideoTaskView.snp.bottom)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(1)
        }

        // 连续签到容器约束
        checkInContainerView.snp.makeConstraints { make in
            make.top.equalTo(publishVideoSeparator.snp.bottom)
            make.left.right.equalToSuperview()
        }

        // 连续签到任务行约束
        checkInTaskView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(80)
        }

        // 签到日历约束
        checkInCalendarView.snp.makeConstraints { make in
            make.top.equalTo(checkInTaskView.snp.bottom).offset(16)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(80)
            make.bottom.equalToSuperview().offset(-16)
        }

        // 连续签到分割线约束
        checkInSeparator.snp.makeConstraints { make in
            make.top.equalTo(checkInContainerView.snp.bottom)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(1)
        }

        // 邀请好友任务行约束
        inviteFriendsTaskView.snp.makeConstraints { make in
            make.top.equalTo(checkInSeparator.snp.bottom)
            make.left.right.equalToSuperview()
            make.height.equalTo(80)
        }

        // 邀请好友分割线约束
        inviteFriendsSeparator.snp.makeConstraints { make in
            make.top.equalTo(inviteFriendsTaskView.snp.bottom)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(1)
        }

        // 观看视频容器约束
        watchVideoContainerView.snp.makeConstraints { make in
            make.top.equalTo(inviteFriendsSeparator.snp.bottom)
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview().offset(-20)
        }

        // 观看视频任务行约束
        watchVideoTaskView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(80)
        }

        // 观看视频进度条约束
        videoProgressView.snp.makeConstraints { make in
            make.top.equalTo(watchVideoTaskView.snp.bottom).offset(16)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(60)
            make.bottom.equalToSuperview().offset(-16)
        }

        // 进度条背景约束
        videoProgressBackgroundView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.left.right.equalToSuperview()
            make.height.equalTo(8)
        }

        // 进度条前景约束
        videoProgressForegroundView.snp.makeConstraints { make in
            make.top.left.height.equalTo(videoProgressBackgroundView)
            make.width.equalTo(videoProgressBackgroundView).multipliedBy(videoWatchProgress)
        }

        // 进度标签约束
        videoProgressLabel.snp.makeConstraints { make in
            make.top.equalTo(videoProgressBackgroundView.snp.bottom).offset(8)
            make.left.equalToSuperview()
        }
    }

    private func setupCheckInCalendar() {
        // 清除之前的子视图
        checkInCalendarView.subviews.forEach { $0.removeFromSuperview() }

        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.distribution = .fillEqually
        stackView.spacing = 8

        checkInCalendarView.addSubview(stackView)
        stackView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // 创建签到日期视图
        for (index, checkInDay) in checkInDays.enumerated() {
            let dayView = createCheckInDayView(checkInDay: checkInDay, isToday: index == currentCheckInDay - 1)
            stackView.addArrangedSubview(dayView)
        }
    }

    private func createCheckInDayView(checkInDay: CheckInDay, isToday: Bool) -> UIView {
        let containerView = UIView()

        // 奖励标签
        let rewardLabel = UILabel()
        rewardLabel.text = "+\(checkInDay.reward)"
        rewardLabel.textColor = checkInDay.isCheckedIn ? .white : UIColor(hex: "#666666")
        rewardLabel.font = .systemFont(ofSize: 12, weight: .medium)
        rewardLabel.textAlignment = .center
        rewardLabel.backgroundColor = checkInDay.isCheckedIn ? .appThemeOrange : UIColor(hex: "#F0F0F0")
        rewardLabel.layer.cornerRadius = 12
        rewardLabel.clipsToBounds = true

        // 日期标签
        let dayLabel = UILabel()
        dayLabel.text = checkInDay.day
        dayLabel.textColor = UIColor(hex: "#333333")
        dayLabel.font = .systemFont(ofSize: 12)
        dayLabel.textAlignment = .center

        containerView.addSubview(rewardLabel)
        containerView.addSubview(dayLabel)

        rewardLabel.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.centerX.equalToSuperview()
            make.width.equalTo(40)
            make.height.equalTo(24)
        }

        dayLabel.snp.makeConstraints { make in
            make.top.equalTo(rewardLabel.snp.bottom).offset(8)
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview()
        }

        return containerView
    }

    private func updateVideoProgress() {
        let progressText = "已观看\(videoWatchedSeconds / 60)分钟，还差\((videoTotalSeconds - videoWatchedSeconds) / 60)分钟"
        videoProgressLabel.text = progressText

        // 更新进度条宽度
        videoProgressForegroundView.snp.updateConstraints { make in
            make.width.equalTo(videoProgressBackgroundView).multipliedBy(videoWatchProgress)
        }
    }

    // MARK: - 事件处理

    @objc private func withdrawButtonTapped() {
        print("提现按钮被点击")
        // TODO: 实现提现功能
        let alert = UIAlertController(title: "提现", message: "提现功能开发中", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    private func publishVideoTaskTapped() {
        print("发布视频任务被点击")
        // TODO: 跳转到发布视频页面
        let alert = UIAlertController(title: "发布视频", message: "跳转到发布视频页面", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    private func checkInTaskTapped() {
        print("签到任务被点击")
        // 执行签到逻辑
        performCheckIn()
    }

    private func inviteFriendsTaskTapped() {
        print("邀请好友任务被点击")
        // TODO: 跳转到邀请好友页面
        let alert = UIAlertController(title: "邀请好友", message: "跳转到邀请好友页面", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    private func watchVideoTaskTapped() {
        print("观看视频任务被点击")
        // TODO: 跳转到视频观看页面
        let alert = UIAlertController(title: "观看视频", message: "跳转到视频观看页面", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    private func performCheckIn() {
        // 检查是否已经签到
        if currentCheckInDay <= checkInDays.count && !checkInDays[currentCheckInDay - 1].isCheckedIn {
            // 执行签到
            checkInDays[currentCheckInDay - 1].isCheckedIn = true
            currentCheckInDay += 1

            // 更新UI
            setupCheckInCalendar()

            // 更新按钮状态
            if currentCheckInDay > checkInDays.count {
                checkInTaskView.updateButtonTitle("已完成")
                checkInTaskView.setCompleted(true)
            }

            // 显示签到成功提示
            let reward = checkInDays[currentCheckInDay - 2].reward
            currentGoldCoins += reward
            goldCoinAmountLabel.text = "\(currentGoldCoins)"

            let alert = UIAlertController(title: "签到成功", message: "获得\(reward)金币奖励", preferredStyle: .alert)
            alert.addAction(UIAlertAction(title: "确定", style: .default))
            present(alert, animated: true)
        } else {
            let alert = UIAlertController(title: "提示", message: "今日已签到", preferredStyle: .alert)
            alert.addAction(UIAlertAction(title: "确定", style: .default))
            present(alert, animated: true)
        }
    }
}

// MARK: - 数据模型

struct CheckInDay {
    let day: String
    let reward: Int
    var isCheckedIn: Bool
}

// MARK: - 任务行组件（无卡片样式）

class TaskRowView: UIView {

    // MARK: - UI 组件
    private lazy var iconImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        imageView.backgroundColor = UIColor(hex: "#F0F0F0")
        imageView.layer.cornerRadius = 20
        imageView.clipsToBounds = true
        return imageView
    }()

    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor(hex: "#333333")
        label.font = .systemFont(ofSize: 16, weight: .medium)
        return label
    }()

    private lazy var subtitleLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor(hex: "#666666")
        label.font = .systemFont(ofSize: 14)
        return label
    }()

    private lazy var rewardLabel: UILabel = {
        let label = UILabel()
        label.textColor = .appThemeOrange
        label.font = .systemFont(ofSize: 14, weight: .medium)
        return label
    }()

    private lazy var actionButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 14, weight: .medium)
        button.backgroundColor = .appThemeOrange
        button.layer.cornerRadius = 16
        button.addTarget(self, action: #selector(actionButtonTapped), for: .touchUpInside)
        return button
    }()

    // MARK: - 属性
    var onButtonTapped: (() -> Void)?
    private var isCompleted: Bool = false

    // MARK: - 初始化
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }

    private func setupUI() {
        backgroundColor = .white

        addSubview(iconImageView)
        addSubview(titleLabel)
        addSubview(subtitleLabel)
        addSubview(rewardLabel)
        addSubview(actionButton)

        setupConstraints()
    }

    private func setupConstraints() {
        iconImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(40)
        }

        titleLabel.snp.makeConstraints { make in
            make.left.equalTo(iconImageView.snp.right).offset(12)
            make.top.equalToSuperview().offset(16)
        }

        subtitleLabel.snp.makeConstraints { make in
            make.left.equalTo(titleLabel)
            make.top.equalTo(titleLabel.snp.bottom).offset(4)
            make.bottom.equalToSuperview().offset(-16)
        }

        actionButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.width.equalTo(60)
            make.height.equalTo(32)
        }

        rewardLabel.snp.makeConstraints { make in
            make.right.equalTo(actionButton.snp.left).offset(-12)
            make.centerY.equalToSuperview()
        }
    }

    // MARK: - 公共方法

    func configure(icon: UIImage?, title: String, subtitle: String, reward: String, buttonTitle: String, isCompleted: Bool) {
        iconImageView.image = icon
        titleLabel.text = title
        subtitleLabel.text = subtitle
        rewardLabel.text = reward
        actionButton.setTitle(buttonTitle, for: .normal)
        setCompleted(isCompleted)
    }

    func setCompleted(_ completed: Bool) {
        isCompleted = completed
        if completed {
            actionButton.backgroundColor = UIColor(hex: "#CCCCCC")
            actionButton.setTitle("已完成", for: .normal)
            actionButton.isEnabled = false
        } else {
            actionButton.backgroundColor = .appThemeOrange
            actionButton.isEnabled = true
        }
    }

    func updateButtonTitle(_ title: String) {
        actionButton.setTitle(title, for: .normal)
    }

    @objc private func actionButtonTapped() {
        if !isCompleted {
            onButtonTapped?()
        }
    }
}
