//
//  GoldCoinTaskCenterUsageExample.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/8/12.
//

import UIKit

// MARK: - 金币任务中心使用示例
class GoldCoinTaskCenterUsageExample: BaseViewController {
    
    // MARK: - UI 组件
    
    private lazy var mainTitleLabel: UILabel = {
        let label = UILabel()
        label.text = "金币任务中心演示"
        label.textColor = UIColor(hex: "#333333")
        label.font = .systemFont(ofSize: 24, weight: .bold)
        label.textAlignment = .center
        return label
    }()
    
    private lazy var descriptionLabel: UILabel = {
        let label = UILabel()
        label.text = "点击下方按钮进入金币任务中心"
        label.textColor = UIColor(hex: "#666666")
        label.font = .systemFont(ofSize: 16)
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()
    
    private lazy var enterButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("进入任务中心", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 18, weight: .medium)
        button.backgroundColor = .appThemeOrange
        button.layer.cornerRadius = 25
        button.addTarget(self, action: #selector(enterButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var featuresLabel: UILabel = {
        let label = UILabel()
        label.text = """
        功能特性：
        
        ✅ 头部金币展示卡片（橙色背景）
        ✅ 当前金币数量显示
        ✅ 提现按钮
        ✅ 今日可赚金币提示
        ✅ 兑换比例说明
        
        📋 任务列表：
        • 发布视频任务（已完成状态）
        • 连续签到任务（可操作签到）
        • 签到日历（7天签到进度）
        • 邀请好友任务
        • 观看视频任务
        • 视频观看进度条
        
        🎨 UI设计：
        • 使用项目统一的颜色规范
        • 卡片式设计风格
        • 圆角阴影效果
        • 响应式布局
        """
        label.textColor = UIColor(hex: "#444444")
        label.font = .systemFont(ofSize: 14)
        label.numberOfLines = 0
        return label
    }()
    
    // MARK: - 生命周期
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
    }
    
    // MARK: - 设置方法
    
    private func setupUI() {
        navTitle = "金币系统演示"
        contentView.backgroundColor = UIColor(hex: "#F7F7F7")
        
        contentView.addSubview(mainTitleLabel)
        contentView.addSubview(descriptionLabel)
        contentView.addSubview(enterButton)
        contentView.addSubview(featuresLabel)
        
        setupConstraints()
    }
    
    private func setupConstraints() {
        mainTitleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(40)
            make.left.right.equalToSuperview().inset(20)
        }

        descriptionLabel.snp.makeConstraints { make in
            make.top.equalTo(mainTitleLabel.snp.bottom).offset(20)
            make.left.right.equalToSuperview().inset(20)
        }
        
        enterButton.snp.makeConstraints { make in
            make.top.equalTo(descriptionLabel.snp.bottom).offset(30)
            make.centerX.equalToSuperview()
            make.width.equalTo(200)
            make.height.equalTo(50)
        }
        
        featuresLabel.snp.makeConstraints { make in
            make.top.equalTo(enterButton.snp.bottom).offset(40)
            make.left.right.equalToSuperview().inset(20)
            make.bottom.lessThanOrEqualToSuperview().offset(-20)
        }
    }
    
    // MARK: - 事件处理
    
    @objc private func enterButtonTapped() {
        let goldCoinTaskCenterVC = GoldCoinSystemTaskCenterViewController()
        navigationController?.pushViewController(goldCoinTaskCenterVC, animated: true)
    }
}

// MARK: - 使用说明
/*
 
 ## 金币任务中心使用指南
 
 ### 1. 基本使用
 ```swift
 let taskCenterVC = GoldCoinSystemTaskCenterViewController()
 navigationController?.pushViewController(taskCenterVC, animated: true)
 ```
 
 ### 2. 图片资源命名
 需要在项目中添加以下图片资源：
 
 - `gold_coin_task_center_header_bg` - 头部橙色背景图
 - `gold_coin_task_publish_video_icon` - 发布视频任务图标
 - `gold_coin_task_checkin_icon` - 签到任务图标
 - `gold_coin_task_invite_friends_icon` - 邀请好友任务图标
 - `gold_coin_task_watch_video_icon` - 观看视频任务图标
 
 ### 3. 功能特性
 
 #### 头部卡片
 - 显示当前金币数量
 - 提现按钮（可自定义跳转逻辑）
 - 今日可赚金币提示
 - 橙色渐变背景
 
 #### 任务系统
 - 发布视频：展示已完成状态
 - 连续签到：可点击签到，更新签到状态
 - 邀请好友：跳转邀请页面
 - 观看视频：显示观看进度条
 
 #### 签到日历
 - 7天签到进度展示
 - 不同奖励金额显示
 - 已签到状态标识
 
 ### 4. 自定义配置
 
 可以通过修改以下属性来自定义数据：
 - `currentGoldCoins`: 当前金币数量
 - `dailyEarnableCoins`: 今日可赚金币
 - `exchangeRate`: 兑换比例
 - `checkInDays`: 签到数据
 - `videoWatchProgress`: 视频观看进度
 
 ### 5. 事件回调
 
 各个任务按钮都有对应的点击事件处理方法，可以根据需要实现具体的跳转逻辑：
 - `publishVideoTaskTapped()`: 发布视频
 - `checkInTaskTapped()`: 签到操作
 - `inviteFriendsTaskTapped()`: 邀请好友
 - `watchVideoTaskTapped()`: 观看视频
 - `withdrawButtonTapped()`: 提现操作
 
 */
