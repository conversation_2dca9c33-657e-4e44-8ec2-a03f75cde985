//
//  CheckInLogicTest.swift
//  签到逻辑测试
//
//  Created by yong<PERSON>ng ye on 2025/8/12.
//

import Foundation

// 测试签到逻辑的辅助函数
func testCheckInLogic() {
    print("=== 签到逻辑测试 ===")
    
    // 模拟今天是周二 (weekday = 3)
    let todayWeekday = 3 // 周二
    let currentDayIndex = todayWeekday == 1 ? 6 : todayWeekday - 2 // 转换为0-6的索引：1

    print("今天是周二，currentDayIndex = \(currentDayIndex)")
    
    // 测试场景1：连续签到0天
    print("\n--- 场景1：连续签到0天 ---")
    testScenario(consecutiveCheckInDays: 0, currentDayIndex: currentDayIndex)
    
    // 测试场景2：连续签到2天
    print("\n--- 场景2：连续签到2天 ---")
    testScenario(consecutiveCheckInDays: 2, currentDayIndex: currentDayIndex)
    
    // 测试场景3：连续签到5天
    print("\n--- 场景3：连续签到5天 ---")
    testScenario(consecutiveCheckInDays: 5, currentDayIndex: currentDayIndex)
}

func testScenario(consecutiveCheckInDays: Int, currentDayIndex: Int) {
    let weekdays = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"]

    // 使用新的逻辑
    let startDayIndex: Int
    if consecutiveCheckInDays == 0 {
        startDayIndex = currentDayIndex
    } else {
        startDayIndex = safeModulo(currentDayIndex - consecutiveCheckInDays, 7)
    }

    print("连续签到天数: \(consecutiveCheckInDays)")
    print("当前日期索引: \(currentDayIndex) (\(weekdays[currentDayIndex]))")
    print("起始日期索引: \(startDayIndex) (\(weekdays[startDayIndex]))")
    print("显示的7天:")

    for i in 0..<7 {
        let dayIndex = (startDayIndex + i) % 7
        let dayName = weekdays[dayIndex]
        let reward = (i + 1) * 10
        let isCheckedIn = consecutiveCheckInDays > 0 && i < consecutiveCheckInDays
        let status = isCheckedIn ? "✅" : "⭕"

        print("  \(dayName): +\(reward)金币 \(status)")
    }
}

func safeModulo(_ a: Int, _ b: Int) -> Int {
    let result = a % b
    return result < 0 ? result + b : result
}

/*
预期结果：

=== 签到逻辑测试 ===
今天是周二，currentDayIndex = 2

--- 场景1：连续签到0天 ---
连续签到天数: 0
起始日期索引: 2
显示的7天:
  周二: +10金币 ⭕
  周三: +20金币 ⭕
  周四: +30金币 ⭕
  周五: +40金币 ⭕
  周六: +50金币 ⭕
  周日: +60金币 ⭕
  周一: +70金币 ⭕

--- 场景2：连续签到2天 ---
连续签到天数: 2
起始日期索引: 7 (周日)
显示的7天:
  周日: +10金币 ✅
  周一: +20金币 ✅
  周二: +30金币 ⭕
  周三: +40金币 ⭕
  周四: +50金币 ⭕
  周五: +60金币 ⭕
  周六: +70金币 ⭕

--- 场景3：连续签到5天 ---
连续签到天数: 5
起始日期索引: 4 (周四)
显示的7天:
  周四: +10金币 ✅
  周五: +20金币 ✅
  周六: +30金币 ✅
  周日: +40金币 ✅
  周一: +50金币 ✅
  周二: +60金币 ⭕
  周三: +70金币 ⭕
*/
