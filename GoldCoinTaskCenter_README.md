# 金币系统任务中心实现文档

## 概述

根据提供的UI设计图，我已经完成了金币系统任务中心的搭建。该页面包含头部金币展示卡片、任务列表、签到日历和观看视频进度条等核心功能。

## 文件结构

```
<PERSON><PERSON><PERSON>qi/Modules/Controller/GoldCoinSystem/
├── GoldCoinSystemTaskCenterViewController.swift  # 主控制器
└── GoldCoinTaskCenterUsageExample.swift          # 使用示例
```

## 核心功能

### 1. 头部金币卡片
- **背景**: #F7F7F7色卡片 + 橙色背景图
- **内容**: 当前金币数量、提现按钮、今日可赚金币提示
- **样式**: 圆角16、阴影效果

### 2. 任务列表
- **发布视频**: 显示已完成状态
- **连续签到**: 可点击签到，实时更新状态
- **邀请好友**: 跳转邀请页面
- **观看视频**: 显示观看进度

### 3. 签到日历
- **7天签到进度**: 周一到周日
- **奖励显示**: 不同天数不同奖励金额
- **状态标识**: 已签到/未签到状态

### 4. 观看视频进度条
- **进度显示**: 橙色进度条
- **文字提示**: "已观看X分钟，还差X分钟"

## 图片资源命名

需要在项目中添加以下图片资源：

```
gold_coin_task_center_header_bg      # 头部橙色背景图
gold_coin_task_publish_video_icon    # 发布视频任务图标
gold_coin_task_checkin_icon          # 签到任务图标  
gold_coin_task_invite_friends_icon   # 邀请好友任务图标
gold_coin_task_watch_video_icon      # 观看视频任务图标
```

## 技术实现

### 1. 架构设计
- 继承自 `BaseViewController`
- 使用 `SnapKit` 进行约束布局
- 自定义 `TaskCard` 组件
- 数据模型 `CheckInDay` 结构体

### 2. UI组件
- **滚动视图**: 支持内容滚动
- **卡片视图**: 统一的卡片样式
- **任务卡片**: 可复用的任务组件
- **进度条**: 自定义进度显示

### 3. 颜色规范
- 主题橙色: `UIColor.appThemeOrange` (#FF8D36)
- 背景灰色: `UIColor.appBackgroundGray` (#F5F5F5)
- 卡片背景: `UIColor(hex: "#F7F7F7")`

## 使用方法

### 1. 基本使用
```swift
let taskCenterVC = GoldCoinSystemTaskCenterViewController()
navigationController?.pushViewController(taskCenterVC, animated: true)
```

### 2. 集成到现有页面
已更新 `VideoDisplayCenterViewController` 中的金币点击事件：
```swift
private func showGoldCoinInfo() {
    let goldCoinTaskCenterVC = GoldCoinSystemTaskCenterViewController()
    navigationController?.pushViewController(goldCoinTaskCenterVC, animated: true)
}
```

## 核心功能实现

### 1. 签到功能
- 点击签到按钮触发 `performCheckIn()` 方法
- 更新签到状态和金币数量
- 刷新签到日历UI

### 2. 任务状态管理
- `TaskCard` 组件支持完成/未完成状态
- 动态更新按钮样式和文字
- 事件回调处理

### 3. 进度条更新
- `updateVideoProgress()` 方法更新进度
- 动态计算进度条宽度
- 实时更新进度文字

## 自定义配置

可以通过修改以下属性来自定义数据：

```swift
private var currentGoldCoins: Int = 8888           # 当前金币数量
private var dailyEarnableCoins: Int = 2000         # 今日可赚金币
private var exchangeRate: String = "10000金币=1元现金"  # 兑换比例
private var videoWatchProgress: Float = 0.3        # 视频观看进度(30%)
```

## 待完善功能

1. **API集成**: 连接后端接口获取真实数据
2. **页面跳转**: 完善各任务的具体跳转逻辑
3. **动画效果**: 添加签到成功、金币增加等动画
4. **数据持久化**: 保存签到状态和进度数据
5. **错误处理**: 添加网络错误和异常处理

## 测试建议

1. 测试签到功能的状态更新
2. 验证各任务按钮的点击响应
3. 检查滚动视图的布局适配
4. 测试不同屏幕尺寸的显示效果

## 注意事项

1. 图片资源需要按照命名规范添加到项目中
2. 确保项目中已正确配置颜色扩展
3. 测试时注意检查约束是否正确
4. 建议在真机上测试滚动和交互效果
