# 金币系统任务中心实现文档

## 概述

根据提供的UI设计图，我已经完成了金币系统任务中心的搭建。该页面包含头部金币展示卡片、任务列表、签到日历和观看视频进度条等核心功能。

## 文件结构

```
<PERSON><PERSON><PERSON>qi/Modules/Controller/GoldCoinSystem/
├── GoldCoinSystemTaskCenterViewController.swift  # 主控制器
└── GoldCoinTaskCenterUsageExample.swift          # 使用示例
```

## 核心功能

### 1. 整体布局
- **背景**: 纯白色背景
- **头部容器**: 距离顶部16pt，#F7F7F7色背景，20圆角，168高度，左右各16pt边距

### 2. 头部金币区域
- **当前金币背景图**: 橙色背景图，位于头部容器上方
- **内容**: 当前金币数量、提现按钮、今日可赚金币提示
- **兑换比例**: 位于当前金币背景图下方+8pt，左边距离背景View +16pt

### 3. 任务列表（无卡片样式）
- **发布视频**: 显示已完成状态，带分割线
- **连续签到**: 包含签到任务行和签到日历，作为一个整体cell
- **邀请好友**: 跳转邀请页面，带分割线
- **观看视频**: 包含观看任务行和进度条，作为一个整体cell

### 4. 分割线设计
- **颜色**: #E5E5E5
- **位置**: 左右各16pt边距
- **高度**: 1pt

### 5. 智能签到日历
- **动态显示逻辑**: 根据当前连续签到天数智能计算显示范围
- **奖励递增**: +10, +20, +30, +40, +50, +60, +70
- **UI规格**: 32*32方块，圆角4pt，距离连续签到图标20pt
- **选中状态**: #FF9143背景色，#FF8F1F文字颜色
- **未选中状态**: #E5E5E5背景色，#7E7E7E文字颜色
- **周几计算**:
  - 连续签到0天：从今天开始显示7天
  - 连续签到N天：倒回N天显示，前N天高亮

### 6. 观看视频进度条
- **进度显示**: 橙色进度条
- **文字提示**: "已观看X分钟，还差X分钟"

## 图片资源命名

需要在项目中添加以下图片资源：

```
gold_coin_task_center_header_bg      # 头部橙色背景图
gold_coin_task_publish_video_icon    # 发布视频任务图标
gold_coin_task_checkin_icon          # 签到任务图标  
gold_coin_task_invite_friends_icon   # 邀请好友任务图标
gold_coin_task_watch_video_icon      # 观看视频任务图标
```

## 技术实现

### 1. 架构设计
- 继承自 `BaseViewController`
- 使用 `SnapKit` 进行约束布局
- 自定义 `TaskRowView` 组件（无卡片样式）
- 数据模型 `CheckInDay` 结构体
- 容器化设计：连续签到和观看视频各自作为独立容器

### 2. UI组件
- **滚动视图**: 支持内容滚动，纯白色背景
- **头部背景容器**: #F7F7F7色背景，20圆角，168高度
- **任务行视图**: 无卡片样式的任务组件
- **分割线**: #E5E5E5色分割线，左右16pt边距
- **进度条**: 自定义进度显示

### 3. 颜色规范
- 主题橙色: `UIColor.appThemeOrange` (#FF8D36)
- 整体背景: 纯白色 `.white`
- 头部背景: `UIColor(hex: "#F7F7F7")`
- 分割线: `UIColor(hex: "#E5E5E5")`

## 使用方法

### 1. 基本使用
```swift
let taskCenterVC = GoldCoinSystemTaskCenterViewController()
navigationController?.pushViewController(taskCenterVC, animated: true)
```

### 2. 集成到现有页面
已更新 `VideoDisplayCenterViewController` 中的金币点击事件：
```swift
private func showGoldCoinInfo() {
    let goldCoinTaskCenterVC = GoldCoinSystemTaskCenterViewController()
    navigationController?.pushViewController(goldCoinTaskCenterVC, animated: true)
}
```

## 核心功能实现

### 1. 签到功能
- 点击签到按钮触发 `performCheckIn()` 方法
- 更新签到状态和金币数量
- 刷新签到日历UI

### 2. 任务状态管理
- `TaskCard` 组件支持完成/未完成状态
- 动态更新按钮样式和文字
- 事件回调处理

### 3. 进度条更新
- `updateVideoProgress()` 方法更新进度
- 动态计算进度条宽度
- 实时更新进度文字

## 自定义配置

可以通过修改以下属性来自定义数据：

```swift
private var currentGoldCoins: Int = 8888           # 当前金币数量
private var dailyEarnableCoins: Int = 2000         # 今日可赚金币
private var exchangeRate: String = "10000金币=1元现金"  # 兑换比例
private var videoWatchProgress: Float = 0.3        # 视频观看进度(30%)
```

## 签到逻辑详解

### 签到日历显示规则

1. **连续签到0天（今天还没签到）**
   - 获取今天是周几（如周二）
   - 显示：周二、周三、周四、周五、周六、周日、周一
   - 奖励：+10、+20、+30、+40、+50、+60、+70
   - 状态：全部未签到（灰色）

2. **连续签到2天**
   - 倒回2天计算起始日期
   - 如今天周二，倒回2天是周日
   - 显示：周日、周一、周二、周三、周四、周五、周六
   - 奖励：+10、+20、+30、+40、+50、+60、+70
   - 状态：前2天已签到（橙色），其余未签到

3. **连续签到5天**
   - 倒回5天计算起始日期
   - 显示对应的7天周期
   - 状态：前5天已签到，后2天未签到

4. **连续签到7天**
   - 显示完整的7天周期
   - 状态：全部已签到，任务完成

### 签到奖励机制

- 第1天：+10金币
- 第2天：+20金币
- 第3天：+30金币
- ...
- 第7天：+70金币

### UI调整说明

1. **Cell高度调整**
   - 发布视频：88pt
   - 连续签到：149pt（包含签到日历）
   - 邀请好友：80pt
   - 观看视频：125pt（包含进度条）

2. **对齐方式优化**
   - +金币数对齐标题
   - 按钮对齐副标题

3. **间距调整**
   - 签到日历距离连续签到图标：20pt
   - 观看视频进度条距离图标下方：16pt

4. **签到日历UI规格**
   - 方块尺寸：32*32pt
   - 圆角：4pt
   - 选中背景色：#FF9143
   - 选中文字色：#FF8F1F
   - 未选中背景色：#E5E5E5
   - 未选中文字色：#7E7E7E

## 待完善功能

1. **API集成**: 连接后端接口获取真实数据
2. **页面跳转**: 完善各任务的具体跳转逻辑
3. **动画效果**: 添加签到成功、金币增加等动画
4. **数据持久化**: 保存签到状态和进度数据
5. **错误处理**: 添加网络错误和异常处理

## 测试建议

1. 测试签到功能的状态更新
2. 验证各任务按钮的点击响应
3. 检查滚动视图的布局适配
4. 测试不同屏幕尺寸的显示效果

## 注意事项

1. 图片资源需要按照命名规范添加到项目中
2. 确保项目中已正确配置颜色扩展
3. 测试时注意检查约束是否正确
4. 建议在真机上测试滚动和交互效果
